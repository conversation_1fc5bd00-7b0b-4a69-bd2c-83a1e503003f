<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Colorful Layout</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background-color: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
        }

        .container {
            width: 320px;
            height: 256px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .top-green {
            background-color: #84cc16;
            height: 25%;
            flex-shrink: 0;
        }

        .yellow-bar {
            background-color: #fde047;
            height: 25%;
            flex-shrink: 0;
        }

        .main-section {
            display: flex;
            height: 50%;
            flex-shrink: 0;
        }

        .purple-left {
            background-color: #8b5cf6;
            width: 25%;
            flex-shrink: 0;
        }

        .middle-area {
            display: flex;
            flex-direction: column;
            width: 50%;
            flex-shrink: 0;
        }

        .cyan-top {
            background-color: #22d3ee;
            height: 50%;
            flex-shrink: 0;
        }

        .bottom-middle {
            display: flex;
            height: 50%;
            flex-shrink: 0;
        }

        .bottom-right {
            display: flex;
            height: 50%;
            flex-shrink: 0;
        }

        .pink-section {
            background-color: #f472b6;
            width: 50%;
            flex-shrink: 0;
        }

        .lime-section {
            background-color: #bef264;
            width: 50%;
            flex-shrink: 0;
        }

        .blue-section {
            background-color: #2563eb;
            width: 50%;
            flex-shrink: 0;
        }

        .coral-right {
            background-color: #f87171;
            width: 25%;
            flex-shrink: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="top-green"></div>
        <div class="yellow-bar"></div>
        <div class="main-section">
            <div class="purple-left"></div>
            <div class="middle-area">
                <div class="cyan-top"></div>
                <div class="bottom-middle">
                    <div class="pink-section"></div>
                    <div class="bottom-right">
                        <div class="lime-section"></div>
                        <div class="blue-section"></div>
                    </div>
                </div>
            </div>
            <div class="coral-right"></div>
        </div>
    </div>
</body>
</html>